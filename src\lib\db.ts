import { prisma } from './prisma'

// Export prisma for use in other files
export { prisma }

// User operations
export async function createUser(data: {
  email: string
  name?: string
  password?: string
  verified?: boolean
}) {
  const user = await prisma.user.create({
    data: {
      email: data.email,
      name: data.name,
      password: data.password,
      credits: 0, // Credits are given only after email verification
    },
  })

  return user
}

export async function getUserByEmail(email: string) {
  return prisma.user.findUnique({
    where: { email },
    include: {
      subscriptions: true,
    },
  })
}

export async function getUserById(id: string) {
  return prisma.user.findUnique({
    where: { id },
    include: {
      subscriptions: true,
    },
  })
}

export async function getUserWithAccountsById(id: string) {
  return prisma.user.findUnique({
    where: { id },
    include: {
      accounts: true,
      subscriptions: true,
    },
  })
}

// Check if a user is an SSO user (has OAuth accounts and no password)
export function isSSO(user: { password?: string | null; accounts?: { provider: string }[] }) {
  // User is SSO if they have OAuth accounts and no password
  const hasOAuthAccounts = user.accounts && user.accounts.some(account =>
    account.provider !== 'credentials' && account.provider !== 'email'
  )
  const hasNoPassword = !user.password

  return hasOAuthAccounts && hasNoPassword
}

// Check if a user has OAuth accounts (regardless of password)
export function hasOAuthAccounts(user: { accounts?: { provider: string }[] }) {
  return user.accounts && user.accounts.some(account =>
    account.provider !== 'credentials' && account.provider !== 'email'
  )
}

// Check if a user has a password
export function hasPassword(user: { password?: string | null }) {
  return !!user.password
}

// Check if user should see password change functionality
export function shouldShowPasswordChange(user: { password?: string | null; accounts?: { provider: string }[] }) {
  // Show password change if user has a password
  // This covers:
  // 1. Pure email/password users
  // 2. Hybrid users (started with email/password, then linked SSO)
  //
  // Don't show for pure SSO users (no password)
  return hasPassword(user)
}

export async function updateUserCredits(userId: string, credits: number) {
  return prisma.user.update({
    where: { id: userId },
    data: { credits },
  })
}

export async function addCreditsToUser(userId: string, creditsToAdd: number) {
  return prisma.user.update({
    where: { id: userId },
    data: {
      credits: {
        increment: creditsToAdd,
      },
    },
  })
}

export async function deductCreditsFromUser(userId: string, creditsToDeduct: number) {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { credits: true },
  })

  if (!user || user.credits < creditsToDeduct) {
    throw new Error('Insufficient credits')
  }

  return prisma.user.update({
    where: { id: userId },
    data: {
      credits: {
        decrement: creditsToDeduct,
      },
    },
  })
}

export async function updateUser(userId: string, data: any) {
  return prisma.user.update({
    where: { id: userId },
    data,
  })
}

export async function updateUserStripeCustomerId(userId: string, stripeCustomerId: string) {
  return prisma.user.update({
    where: { id: userId },
    data: { stripeCustomerId },
  })
}

export async function getUserByStripeCustomerId(stripeCustomerId: string) {
  return prisma.user.findUnique({
    where: { stripeCustomerId },
    include: {
      subscriptions: true,
    },
  })
}

// Verify user email and give welcome credits
export async function verifyUserEmail(userId: string) {
  // Use a transaction to ensure atomicity and prevent race conditions
  return await prisma.$transaction(async (tx) => {
    // First check if user is already verified to prevent duplicate credit granting
    const existingUser = await tx.user.findUnique({
      where: { id: userId }
    })

    if (!existingUser) {
      throw new Error('User not found')
    }

    if (existingUser.emailVerified) {
      // User is already verified, don't grant credits again
      console.log(`⚠️ User ${existingUser.email} is already verified, skipping credit grant`)
      return existingUser
    }

    // Check if welcome credits were already granted (additional safety check)
    const existingWelcomeTransaction = await tx.transaction.findFirst({
      where: {
        userId: userId,
        type: 'free_credits',
        description: 'Welcome bonus: 10 free credits'
      }
    })

    if (existingWelcomeTransaction) {
      console.log(`⚠️ Welcome credits already granted to user ${existingUser.email}, only updating verification status`)
      // Just update verification status without granting credits
      return await tx.user.update({
        where: { id: userId },
        data: {
          emailVerified: new Date(),
        },
      })
    }

    const user = await tx.user.update({
      where: { id: userId },
      data: {
        emailVerified: new Date(),
        credits: {
          increment: 10, // Give 10 welcome credits
        },
      },
    })

    // Create transaction record for welcome credits
    await tx.transaction.create({
      data: {
        userId: user.id,
        type: 'free_credits',
        amount: 0,
        currency: 'HKD',
        status: 'completed',
        credits: 10,
        description: 'Welcome bonus: 10 free credits',
        metadata: {
          type: 'welcome_bonus',
          reason: 'email_verification'
        }
      }
    })

    console.log(`✅ User ${user.email} verified and granted 10 welcome credits`)
    return user
  })
}

// DISABLED: Clean up unverified accounts - DO NOT DELETE USER ACCOUNTS
// This function has been disabled to prevent accidental deletion of user accounts
// including SSO accounts that may not have completed email verification
export async function cleanupExpiredUnverifiedAccounts() {
  // DISABLED: No longer deleting unverified accounts
  // Instead, we only expire verification tokens, not user accounts
  return 0

  /* ORIGINAL DANGEROUS CODE - COMMENTED OUT
  const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)

  try {
    const result = await prisma.user.deleteMany({
      where: {
        emailVerified: null,
        createdAt: {
          lt: oneDayAgo
        }
      }
    })

    console.log(`🧹 Cleaned up ${result.count} expired unverified accounts`)
    return result.count
  } catch (error) {
    console.error('Error cleaning up expired accounts:', error)
    return 0
  }
  */
}

// Subscription operations
export async function createSubscription(data: {
  userId: string
  stripeSubscriptionId?: string
  stripeCustomerId?: string
  status: string
  productName?: string
  currentPeriodStart?: Date
  currentPeriodEnd?: Date
}) {
  return prisma.subscription.create({
    data,
    include: {
      user: true,
    },
  })
}

export async function updateSubscription(
  subscriptionId: string,
  data: any
) {
  return prisma.subscription.update({
    where: { id: subscriptionId },
    data,
    include: {
      user: true,
    },
  })
}

export async function getActiveSubscription(userId: string) {
  return prisma.subscription.findFirst({
    where: {
      userId,
      status: 'active',
    },
  })
}

export async function getSubscriptionByStripeId(stripeSubscriptionId: string) {
  return prisma.subscription.findUnique({
    where: { stripeSubscriptionId },
    include: {
      user: true,
    },
  })
}

// Transaction operations
export async function createTransaction(data: {
  userId: string
  type: string
  amount: number
  currency?: string
  status: string
  stripePaymentIntentId?: string
  stripeInvoiceId?: string
  stripeSessionId?: string
  credits?: number
  description?: string
  metadata?: any
  // Expanded metadata fields
  stripeSubscriptionId?: string
  packageName?: string
  packageCredit?: number
  previousPackage?: string
  previousPackageCredit?: number
  creditDifference?: number
  billingReason?: string
  canceledAt?: string
  reason?: string
  renewalDate?: string
  nextRenewalDate?: string
  periodEnd?: string
  cancelAtPeriodEnd?: boolean
  previousCredits?: number
}) {
  return prisma.transaction.create({
    data: {
      ...data,
      currency: data.currency || 'HKD',
    },
    include: {
      user: true,
    },
  })
}

export async function updateTransaction(
  transactionId: string,
  data: any
) {
  return prisma.transaction.update({
    where: { id: transactionId },
    data,
    include: {
      user: true,
    },
  })
}

export async function getTransactionsByUser(userId: string, limit = 10) {
  return prisma.transaction.findMany({
    where: { userId },
    orderBy: {
      createdAt: 'desc',
    },
    take: limit,
  })
}

export async function getLastTransactionByUser(userId: string, type?: string) {
  return prisma.transaction.findFirst({
    where: {
      userId,
      ...(type && { type })
    },
    orderBy: {
      createdAt: 'desc',
    },
  })
}

// Usage tracking for document analysis operations
export async function recordUsage(data: {
  userId: string
  operationId?: string
  operationType?: string
  creditSpent?: number
  status?: string
  fileName?: string
  fileSize?: number
  metadata?: any
}) {
  try {
    return await prisma.usageRecord.create({
      data: {
        userId: data.userId,
        operationId: data.operationId,
        operationType: data.operationType || 'document_analysis',
        creditSpent: data.creditSpent || 1,
        status: data.status || 'pending',
        fileName: data.fileName,
        fileSize: data.fileSize,
        metadata: data.metadata,
      },
    })
  } catch (error: any) {
    console.error('Error in recordUsage:', error)
    // If usage tracking fails, don't block the main operation
    // Just log the error and continue
    return null
  }
}

export async function getUsageByUser(userId: string, limit = 50) {
  return prisma.usageRecord.findMany({
    where: { userId },
    orderBy: {
      createdAt: 'desc',
    },
    take: limit,
  })
}

// Enhanced function for paginated usage records with filtering and search
export async function getUsageByUserPaginated(
  userId: string,
  options: {
    page?: number
    limit?: number
    status?: string
    search?: string
    dateFrom?: Date
    dateTo?: Date
  } = {}
) {
  const {
    page = 1,
    limit = 20,
    status,
    search,
    dateFrom,
    dateTo
  } = options

  const skip = (page - 1) * limit

  // Build where clause
  const where: any = { userId }

  // Status filter
  if (status && status !== 'all') {
    where.status = status
  }

  // Search by filename (MySQL doesn't support mode: 'insensitive', so we'll use contains which is case-insensitive by default in MySQL)
  if (search && search.trim()) {
    where.fileName = {
      contains: search.trim()
    }
  }

  // Date range filter
  if (dateFrom || dateTo) {
    where.createdAt = {}
    if (dateFrom) {
      where.createdAt.gte = dateFrom
    }
    if (dateTo) {
      // Set to end of day for dateTo
      const endOfDay = new Date(dateTo)
      endOfDay.setHours(23, 59, 59, 999)
      where.createdAt.lte = endOfDay
    }
  }

  // Get total count for pagination
  const total = await prisma.usageRecord.count({ where })

  // Get records
  const records = await prisma.usageRecord.findMany({
    where,
    orderBy: {
      createdAt: 'desc',
    },
    skip,
    take: limit,
  })

  const totalPages = Math.ceil(total / limit)

  return {
    records,
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    }
  }
}

// Update usage record status (for webhook callbacks)
export async function updateUsageRecord(operationId: string, data: {
  status?: string
  fileLink?: string
  completedAt?: Date
  metadata?: any
}) {
  try {
    return await prisma.usageRecord.update({
      where: { operationId },
      data: {
        ...data,
        updatedAt: new Date(),
      },
    })
  } catch (error: any) {
    console.error('Error updating usage record:', error)
    return null
  }
}

// Get usage record by operation ID
export async function getUsageRecordByOperationId(operationId: string) {
  return prisma.usageRecord.findUnique({
    where: { operationId },
    include: { user: true },
  })
}

// Get usage history for analytics (aggregated by day)
export async function getUsageHistory(userId: string, days = 30) {
  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(endDate.getDate() - days)
  startDate.setHours(0, 0, 0, 0)
  endDate.setHours(23, 59, 59, 999)

  // Get raw usage records and aggregate by day
  const records = await prisma.usageRecord.findMany({
    where: {
      userId,
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
      status: 'completed', // Only count completed operations
    },
    select: {
      createdAt: true,
      creditSpent: true,
    },
    orderBy: {
      createdAt: 'desc',
    },
  })

  // Aggregate by day
  const dailyUsage = new Map()
  records.forEach(record => {
    const day = record.createdAt.toISOString().split('T')[0]
    if (!dailyUsage.has(day)) {
      dailyUsage.set(day, { date: day, count: 0, credits: 0 })
    }
    const dayData = dailyUsage.get(day)
    dayData.count += 1
    dayData.credits += record.creditSpent
  })

  return Array.from(dailyUsage.values()).sort((a, b) => b.date.localeCompare(a.date))
}

// Product operations removed - using Stripe as single source of truth

// Webhook operations removed - no longer storing webhook events in database

// Support ticket operations
export async function createSupportTicket(data: {
  name: string
  email: string
  phone?: string
  subject: string
  message: string
}) {
  // Generate case ID with # prefix and 10 digits
  const caseId = '#' + Math.random().toString().slice(2, 12).padStart(10, '0')

  return prisma.supportTicket.create({
    data: {
      caseId,
      name: data.name,
      email: data.email,
      phone: data.phone,
      subject: data.subject,
      message: data.message,
    },
  })
}

export async function getSupportTicketByCaseId(caseId: string) {
  return prisma.supportTicket.findUnique({
    where: { caseId },
  })
}

export async function updateSupportTicketStatus(caseId: string, status: string) {
  return prisma.supportTicket.update({
    where: { caseId },
    data: { status },
  })
}
